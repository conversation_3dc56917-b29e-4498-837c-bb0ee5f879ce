<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>工作站详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: #f5f6fa;
      padding-bottom: 20px;
    }

    /* 顶部背景区域 */
    .workstation_details_top {
      position: relative;
      z-index: 1;
    }

    .workstation_details_top_img {
      width: 100%;
      height: 220px;
    }

    .header_title {
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      margin-bottom: 10px;
      text-align: center;
      margin-top: 30px;
    }

    .area_tags {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
    }

    .area_tag {
      background: rgba(255, 255, 255, 0.3);
      color: #fff;
      padding: 2px 12px;
      border-radius: 10px;
      font-size: 14px;
      margin: 0 5px;
    }

    /* 工作站信息卡片 */
    .workstation_card {
      background: #fff;
      margin: -30px 16px 16px;
      border-radius: 12px;
      padding: 5px 16px 16px 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      z-index: 2;
      position: relative;
    }

    .workstation_card_tag {
      background: #FAF2E0;
      border-radius: 4px;
      padding: 4px 16px;
      margin-top: 10px;
      margin-right: 10px;
    }

    .icon_model {
      width: 35px;
      height: 40px;
      margin-right: 10px;
    }

    .workstation_card_tag_text {
      font-size: 14px;
      color: #8B5413;
    }

    .station_name {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      margin-top: 10px;
    }

    .info_row {
      margin-bottom: 10px;
    }

    .info_icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .info_label {
      font-size: 14px;
      color: #666;
      width: 65px;
    }

    .info_value {
      font-size: 14px;
      color: #333;
      flex: 1;
    }

    /* 留言按钮 */
    .message_button {
      background: linear-gradient(90deg, #0D75FF 0%, #41BBFF 100%);
      color: #fff;
      border: none;
      border-radius: 20px;
      width: 100%;
      height: 40px;
      font-size: 16px;
      margin-top: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }

    .message_icon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }

    /* 值班信息区域 */
    .duty_section {
      background: #fff;
      margin: 0 16px 16px;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .no_duty {
      text-align: center;
      color: #999;
      padding: 20px 0;
      font-size: 14px;
    }

    /* 值班项目样式 */
    .duty_item {
      margin-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .duty_item:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }

    .duty_header {
      margin-bottom: 8px;
    }

    .duty_status {
      padding: 4px 8px;
      border-radius: 20px;
      font-size: 12px;
      background: #E5F0FF;
      color: #0D6AD8;
    }

    .duty_time {
      font-size: 12px;
      color: #666;
    }

    .duty_theme {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 10px;
    }

    .duty_members {
      background-color: #F0F4FF;
      border-radius: 6px;
      padding: 12px 16px;
      margin-bottom: 10px;
    }

    .member_avatar {
      width: 46px;
      height: 45px;
      background: #D9D9D9;
      border-radius: 50%;
    }

    .member_info {
      margin-left: 20px;
      flex: 1;
    }

    .member_name_tag {
      margin-bottom: 6px;
    }

    .member_name {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      margin-right: 15px;
    }

    .member_tag {
      font-size: 12px;
      color: #0D6AD8;
      background: #E5F0FF;
      border-radius: 5px;
      padding: 4px 8px;
    }

    .member_position {
      font-weight: 500;
      font-size: 12px;
      color: #999999;
    }

    /* 驻站代表区域 */
    .representative_section {
      background: #fff;
      margin: 0 16px 16px;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .representative_list {
      overflow-y: hidden;
    }

    .representative_item {
      height: 140px;
      margin-right: 10px;
      border: 1px #eff2f5 solid;
      text-align: center;
    }

    .representative_item:last-child {
      border-bottom: none;
    }

    .avatar {
      width: 74px;
      height: 84px;
    }

    .rep_name {
      margin-top: 5px;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .rep_position {
      margin: 2px 0;
      font-size: 12px;
      font-weight: 500;
      color: #999999;
    }

    /* 一站一品 */
    .one_station_one_product {
      background: #fff;
      margin: 0 16px 16px;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .one_product_title_area {
      padding-right: 20px;
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-right: 15px;
      line-height: 1.4;
      border-right: 1px solid #EEEEEE;
    }

    .one_station {
      color: #000;
    }

    .one_product {
      color: #FF3B30;
    }

    .one_product_content_title {
      font-size: 18px;
      font-weight: 800;
      color: #000;
      margin-bottom: 8px;
    }

    .one_product_content_text {
      font-size: 16px;
      color: #666;
      line-height: 1.5;
    }

    /* tab信息 */
    .tab_content_card {
      background: #fff;
      margin: 0 16px 16px;
      border-radius: 12px;
      padding: 0 16px 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .introduce {
      font-size: 15px;
      padding: 16px 0;
      font-family: PingFang SC-Medium;
      line-height: 24px;
      color: #333333;
      text-indent: 2em;
    }

    .van-search {
      padding: 0;
    }

    .search_box {
      padding: 10px 0;
    }

    /* 民情民意相关 */
    .public_opinion_item {
      padding: 14px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .opinion_title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      line-height: 22px;
      margin-right: 10px;
    }

    .status_pending {
      padding: 2px 10px;
      border-radius: 5px;
      font-size: 12px;
      white-space: nowrap;
      color: #FF6B35;
      background-color: #FFF2ED;
    }

    .status_evaluated {
      padding: 2px 10px;
      border-radius: 5px;
      font-size: 12px;
      white-space: nowrap;
      color: #36B37E;
      background-color: #E6F7F0;
    }

    .status_reply {
      padding: 2px 10px;
      border-radius: 5px;
      font-size: 12px;
      white-space: nowrap;
      color: rgb(56, 148, 255);
      background-color: rgba(56, 148, 255, 0.1);
    }

    .opinion_author {
      font-size: 14px;
      color: #666;
    }

    .opinion_date {
      font-size: 14px;
      color: #999;
    }

    /* Van-Tabs左右滑动样式 */
    .custom-scrollable-tabs .van-tabs__nav-wrap {
      overflow-x: auto !important;
      overflow-y: hidden !important;
    }

    .custom-scrollable-tabs .van-tabs__nav {
      overflow-x: auto !important;
      overflow-y: hidden !important;
      width: auto !important;
      min-width: 100% !important;
      flex-wrap: nowrap !important;
      white-space: nowrap !important;
    }

    .van-tab {
      padding: 0 10px;
    }

    .custom-scrollable-tabs .van-tabs__nav::-webkit-scrollbar {
      display: none;
    }

    .custom-scrollable-tabs .van-tabs__nav--line {
      padding-bottom: 8px;
    }

    .custom-scrollable-tabs .van-tabs__line {
      bottom: 8px !important;
    }

    /* 隐藏vant自带的左右箭头 */
    .custom-scrollable-tabs .van-tabs__nav__arrow {
      display: none !important;
    }

    /* 信息发布相关 */
    .info_tab_container {
      overflow-x: auto;
      white-space: nowrap;
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .info_tab_container::-webkit-scrollbar {
      display: none;
    }

    .info_tab_item {
      padding: 6px 16px;
      margin-right: 10px;
      font-size: 14px;
      color: #666;
      border-radius: 16px;
      background-color: #f5f5f5;
      flex-shrink: 0;
    }

    .info_tab_item.active {
      color: #3894FF;
      background-color: #E6F2FF;
      font-weight: 500;
    }

    .info_item {
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .info_title {
      font-size: 17px;
      font-weight: 500;
      color: #333;
      line-height: 22px;
      margin-bottom: 10px;
    }

    .info_date {
      font-size: 14px;
      color: #999;
    }

    /* 站点活动相关 */
    .activity_item {
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .activity_title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      line-height: 22px;
      margin-bottom: 10px;
    }

    .activity_time {
      font-size: 14px;
      color: #666;
    }

    .activity_status {
      padding: 2px 10px;
      font-size: 12px;
      color: #666;
      background-color: #f5f5f5;
      border-radius: 6px;
      white-space: nowrap;
    }

    .nodata {
      margin: 30px 0;
      text-align: center;
      color: #ccc;
      width: 100%;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 顶部背景区域 -->
    <div class="workstation_details_top">
      <img :src="info.showImgs?fileImgUrl+info.showImgs:'../img/workstation_default.png'" mode=""
        class="workstation_details_top_img" />
    </div>

    <!-- 工作站信息卡片 -->
    <div class="workstation_card">
      <div class="flex_box flex_justify_between">
        <div class="flex_wrap flex_box flex_align_center flex_1">
          <div class="flex_box workstation_card_tag" v-cloak v-if="info.excellentStationType.value">
            <img src="../img/icon_model.png" mode="" class="icon_model" />
            <span class="workstation_card_tag_text">{{info.excellentStationType.name}}</span>
          </div>
          <div class="workstation_card_tag" v-if="info.cityName" v-cloak>
            <span class="workstation_card_tag_text">{{info.cityName}}</span>
          </div>
          <div class="workstation_card_tag" v-if="info.countryName" v-cloak>
            <span class="workstation_card_tag_text">{{info.countryName}}</span>
          </div>
        </div>
      </div>

      <h2 class="station_name two_text" v-cloak>{{info.name || '阳谷县狮子楼街道顺发社区人大代表工作室'}}</h2>

      <div class="info_row flex_box flex_align_center">
        <img src="../img/icon_user.png" alt="联系人" class="info_icon" />
        <span class="info_label">联系人</span>
        <span class="info_value" v-cloak>{{info.contactUserName || '张武'}}</span>
      </div>

      <div class="info_row flex_box flex_align_center">
        <img src="../img/icon_phone.png" alt="电话" class="info_icon" />
        <span class="info_label">电话</span>
        <span class="info_value" v-cloak>{{info.contactTelephone || '15566666666'}}</span>
      </div>

      <div class="info_row flex_box flex_align_center">
        <img src="../img/icon_time.png" alt="接待时间" class="info_icon" />
        <span class="info_label">接待时间</span>
        <span class="info_value" v-cloak>{{info.openTime || '8:00-18:00'}}</span>
      </div>

      <div class="info_row flex_box flex_align_center">
        <img src="../img/icon_location.png" alt="站点地址" class="info_icon" />
        <span class="info_label">站点地址</span>
        <span class="info_value two_text" v-cloak>{{info.address || '阳谷县狮子楼街道顺发社区人大代表工作室'}}</span>
      </div>

      <button class="message_button" @click="goToMessage">
        <img src="../img/icon_msg.png" alt="留言" class="message_icon" />
        我要留言
      </button>
    </div>

    <!-- 今日值班信息 -->
    <div class="duty_section">
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <span class="section-title">今日代表值班</span>
      </div>
      <!-- 有值班信息时显示 -->
      <div v-if="dutyList && dutyList.length > 0" v-cloak>
        <div class="duty_item" v-for="(duty, index) in dutyList" :key="index">
          <div class="duty_header flex_box flex_justify_between flex_align_center">
            <div class="duty_time">{{ formatTime(duty.startTime) }} - {{ formatTime(duty.endTime) }}</div>
            <div class="duty_status">{{ duty.status }}</div>
          </div>
          <div class="duty_theme">{{ duty.theme }}</div>
          <div class="duty_members flex_box flex_align_center" v-for="(member, mIndex) in duty.memberList" :key="mIndex"
            @click="goToLeaveMessageToRepresentative(member)">
            <img :src="member.photo ? fileImgUrl + member.photo : '../img/def_head_img.jpg'" alt="头像"
              class="member_avatar" />
            <div class="member_info">
              <div class="member_name_tag flex_box flex_align_center">
                <div class="member_name">{{ member.userName }}</div>
                <div class="member_tag">
                  {{member.topUserRole=='nation'?'国':member.topUserRole=='province'?'省':member.topUserRole=='city'?'市':member.topUserRole=='county'?'区/县':member.topUserRole=='town'?'乡镇(街道)':''}}人大代表
                </div>
              </div>
              <div class="member_position">{{member.position}}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 无值班信息时显示 -->
      <div v-else class="no_duty">今日暂无代表值班</div>
    </div>

    <!-- 驻站代表列表 -->
    <div class="representative_section">
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <span class="section-title" v-cloak>驻站代表（{{representatives.length || 0}}）</span>
      </div>
      <div class="representative_list flex_box">
        <template v-if="representatives&&representatives.length>0">
          <div class="representative_item" v-for="(item, index) in representatives" :key="index"
            @click="goToLeaveMessageToRepresentative(item)">
            <img class="avatar"
              :src="item.headImg?fileImgUrl+item.headImg:item.photo?fileImgUrl+item.photo:'../img/def_head_img.jpg'"
              alt="头像" />
            <div class="rep_name two_text" v-cloak>{{item.userName}}</div>
            <div class="rep_position two_text" v-cloak>
              {{item.topUserRole=='nation'?'国':item.topUserRole=='province'?'省':item.topUserRole=='city'?'市':item.topUserRole=='county'?'区县':item.topUserRole=='town'?'乡镇(街道)':''}}人大代表
            </div>
          </div>
        </template>
        <template v-else>
          <div class="nodata">暂无数据</div>
        </template>
      </div>
    </div>

    <!-- 一站一品 -->
    <div class="one_station_one_product" v-if="featuredProductShow">
      <div class="flex_box flex_align_center">
        <div class="one_product_title_area">
          <div class="one_station">一站</div>
          <div class="one_product">一品</div>
        </div>
        <div class="flex_1" @click="openFeaturedMore">
          <div class="one_product_content_title one_text" v-cloak>{{featuredProduct.title || '标题标题'}}</div>
          <div class="one_product_content_text one_text" v-cloak>{{featuredProduct.name || '测试'}}</div>
        </div>
      </div>
    </div>

    <!-- 信息 -->
    <div class="tab_content_card">
      <van-tabs v-model="active" @click="onClickTab" color="#3894FF" title-active-color="#333333"
        title-inactive-color="#999999" class="custom-scrollable-tabs">
        <van-tab title="简介">
          <div class="introduce" v-cloak>{{info.introduction || '暂无'}}</div>
        </van-tab>
        <van-tab title="民情民意" v-cloak>
          <div class="search_box">
            <van-search v-model="searchKeyword" placeholder="请输入关键词" @search="onSearch" />
          </div>
          <template v-if="publicOpinionData&&publicOpinionData.length>0">
            <div class="public_opinion_list" v-for="item in publicOpinionData" @click="openStationLetterDetails(item)">
              <div class="public_opinion_item">
                <div class="flex_box flex_align_center" style="margin-bottom: 10px;">
                  <div class="opinion_title flex_1">{{item.title}}</div>
                  <div
                    :class="item.letterStatus==='未回复'?'status_pending':item.letterStatus==='已评价'?'status_evaluated':item.letterStatus==='已回复'?'status_reply':''">
                    {{item.letterStatus}}
                  </div>
                </div>
                <div class="flex_box flex_align_center flex_justify_between">
                  <div class="opinion_author">{{item.senderUserName.charAt(0) + '*'.repeat(item.senderUserName.length -
                    1)}}</div>
                  <div class="opinion_date">{{formatTime(item.receiveTime)}}</div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
        <van-tab title="信息发布" v-cloak>
          <div class="info_tab_container flex_box" v-if="infomationCategoryColumn.length>0">
            <div :class="['info_tab_item', {active: index === 0}]" @click="selectTab(item, index)"
              v-for="(item, index) in infomationCategoryColumn" :key="item.id">{{item.name}}</div>
          </div>
          <div class="search_box">
            <van-search v-model="searchKeyword" placeholder="请输入关键词" @search="onSearch" />
          </div>
          <template v-if="infoList&&infoList.length!==0">
            <div class="info_list" v-for="item in infoList" @click="openInformationReleaseDetails(item)">
              <div class="info_item">
                <div class="info_title two_text">{{item.infoTitle}}</div>
                <div class="info_date">{{formatTime(item.pubTime)}}</div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
        <van-tab title="站点活动" v-cloak>
          <div class="info_tab_container flex_box" v-if="activityCategoryColumn.length>0">
            <div v-for="(item, index) in activityCategoryColumn" :key="item.id"
              :class="['info_tab_item', {active: index === 0}]" @click="selectTab(item, index)">{{item.name}}</div>
          </div>
          <div class="search_box">
            <van-search v-model="searchKeyword" placeholder="请输入关键词" @search="onSearch" />
          </div>
          <template v-if="activityList&&activityList.length!==0">
            <div class="activity_list" v-for="item in activityList" @click="activityItemClick(item)">
              <div class="activity_item">
                <div class="activity_title">{{item.title}}</div>
                <div class="flex_box flex_align_center flex_justify_between">
                  <div class="activity_time flex_1">{{formatTime(item.beginTime)}}至{{formatTime(item.endTime)}}</div>
                  <div class="activity_status">{{item.activityStatus}}</div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
        <van-tab title="国家机关进站" v-cloak>
          <div class="info_tab_container flex_box" v-if="stateStationCategoryColumn.length>0">
            <div :class="['info_tab_item', {active: index === 0}]" @click="selectTab(item, index)"
              v-for="(item, index) in stateStationCategoryColumn" :key="item.id">{{item.name}}</div>
          </div>
          <div class="search_box">
            <van-search v-model="searchKeyword" placeholder="请输入关键词" />
          </div>
          <template v-if="stateStationList&&stateStationList.length!==0">
            <div class="info_list" v-for="item in stateStationList" @click="stateStationItemClick(item)">
              <div class="info_item">
                <div class="info_title two_text">{{item.infoTitle}}</div>
                <div class="info_date">{{formatTime(item.pubTime)}}</div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata">暂无数据</div>
          </template>
        </van-tab>
      </van-tabs>
    </div>
  </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>

  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicWorkstationInfoKey = '0484addf5648489d14bd612faa4a01f06e3d5cda1959303dd4ab348a4a3830a2ac4b919a85c603c7e319dc96ed240bbc624e32b2c891d54b52fe066770bd53c211' 	// 获取代表工作站详情公钥
    var publicStationOnDutyKey = '045c50a283ee7ef900d83f75f66177466cf1cc283de37868de747cb4b921bf6f1fc067fd9a5757a79fa3f17aa1bd42b60b3c614127a4bd606dcd972b3a5846b111' // 获取今日代表值班列表公钥
    var publicStationMemberKey = '0404de66465d4a9776c5dd6cabf1eda5c5034b882ea062b8cadbd0d6e598bf526c6742a6e0971c86c988ec352b2e29f0031bcf701a7adfefc33a8796289c5ff735' // 获取驻站代表列表公钥
    var publicStationNewsKey = '0406e11737bd4c9ae35b20176e0e6079f0a3fbd646a8106724ca438a43d20767fb259e7bee9b2d77b2a65aa189052587c9d4d4bfcaf4b333f305792241e1db92bb' // 获取站点资讯公钥
    var publicStationLetterKey = '04016af8ac67cb2dffba1c2f4a627b35e74de2a67be68a355a79d8e01b3fc5eecae47db9adff011aac9e8209a7f3c9fd7cb4e1eef911aab22c0a5135104c1fd2ba' // 获取民情民意公钥
    var publicReleaseColumnKey = '040904a12d9fc4af89d91fec1b55a19d50c5378b4159e6a607b85a43ea5eacd5acb933ae2fe0a2a8b764a2bc2fb25d3432fd370dad70f06bd04115a1d68317fcde'// 获取信息发布栏目公钥
    var publicStationActivityTypeKey = '0442b5eaf04728ae1f9e1b93895921304f08e5321152c0012221bb3cdc9ce7c4174d751902077e4d0d1edab80c7c94a5f17c0881aa18e5aee56e13cf6029e4f15f'// 获取站点活动栏目公钥
    var publicStationActivityKey = '04b9d1e87ad288fd8799a379a9b6243004f9019a99228043d4a65ef3eefcf75656b8c846b63b0f4d4c6e65cd29c37c675da8f86c47866e63c7a67b28a38f22cf77'// 获取站点活动列表公钥
    var publicStateStationColumnKey = '04395416e84bdf2b98843cc44bdb18b06dd8e53337cfea2ce4ebda7377475adc9757657791ec6cd08134398c2a6bc7ae83d3dceb1df4e76e734accfae7ea9832df'// 获取国家机关进站栏目公钥



    var id = '', infoColumnId = ''
    var app = new Vue({
      el: "#app",
      data: {
        fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/',
        info: {},
        dutyList: [],
        representatives: [],
        featuredProductShow: false,
        featuredProduct: {},
        active: 0,
        searchKeyword: '',
        // 民情民意
        publicOpinionData: [],
        // 信息发布
        infomationCategoryColumn: [],
        infoList: [],
        // 站点活动
        activityCategoryColumn: [
          // { "id": "1983099327161065473", "name": "普法1", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          // { "id": "1983099327161065472", "name": "普法2", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          // { "id": "1983099327161065471", "name": "普法3", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          // { "id": "1983099327161065470", "name": "普法4", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          // { "id": "1983099327161065469", "name": "普法5", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          // { "id": "1983099327161065468", "name": "普法6", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] }
        ],
        activityList: [
          // { "id": "1983795482446561281", "stationId": "1874029185060597762", "title": "测试活动主题", "titlePic": null, "stationActivityType": { "value": "kchd", "label": "考察活动", "name": "考察活动", "dictCode": "station_activity_type" }, "stationActivityClass": { "value": null, "label": null, "name": null, "dictCode": null }, "address": "", "beginTime": 1761753600000, "endTime": 1761840000000, "stationContentType": { "value": null, "label": null, "name": null, "dictCode": null }, "content": "<p><span style=\"font-family: 微软雅黑; font-size: 12pt;\">测试</span></p>", "personCount": 2, "attachmentIds": "", "isTop": 0, "sort": 0, "needTextMessageNotice": 0, "currentMessageTemplate": "", "isSendbox": 1, "isSendtext": 0, "createBy": "Admin", "createDate": 1761808674810, "stationName": "阳谷县狮子楼街道顺发社区人大代表工作室", "activityStatus": "进行中" }
        ],
        // 国家机关进站
        stateStationCategoryColumn: [
          // { "id": "1983099327161065473", "name": "普法1", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          // { "id": "1983099327161065472", "name": "普法2", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          // { "id": "1983099327161065471", "name": "普法3", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          // { "id": "1983099327161065470", "name": "普法4", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
          // { "id": "1983099327161065469", "name": "普法5", "parentId": "0", "sort": null, "isUsing": 1, "isIgnoreArea": 0, "createBy": "1", "createDate": 1761642698457, "stationId": "1874029185060597762", "businessCode": "news", "label": "普法", "children": [] },
        ],
        stateStationList: [
          // { "id": "1983099367132782594", "stationId": "1874029185060597762", "columnId": "普法", "infoTitle": "这是信息发布**********6666666666", "infoSubtitle": null, "stationShowCode": { "value": "TUWEN", "label": "图文", "name": "图文", "dictCode": "station_show_code" }, "infoSource": null, "pubTime": 1761642699000, "infoVideo": "", "infoVideoType": "1", "infoPic": "", "infoPicType": "2", "contentType": 1, "stationContentType": { "value": null, "label": null, "name": null, "dictCode": null }, "infoContent": "<p><span style=\"font-family: 微软雅黑; font-size: 12pt;\">这是信息发布**********1</span></p>", "linkUrl": "", "isTop": 0, "isShare": 0, "readCount": 2, "attachmentIds": "", "sort": null, "createBy": "Admin", "createDate": 1761642707987, "businessCode": "news", "stationName": "阳谷县狮子楼街道顺发社区人大代表工作室" }
        ],

      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          this.getStationDetail()
          this.getDutyList()
          this.getRepresentatives()
          this.getFeaturedProduct()
        }
      },
      methods: {
        // 获取工作站详情
        getStationDetail () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'workerStationInfo'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicWorkstationInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.info = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getStationDetail()
            }
          })
        },

        // 获取值班信息
        getDutyList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationOnDutyList'
          const now = new Date();
          const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
          const startTime = todayStart.getTime();
          const endTime = todayEnd.getTime();
          var interfacecontent = {
            endTime: endTime.toString(),
            startTime: startTime.toString(),
            stationId: id
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationOnDutyKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.dutyList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getDutyList()
            }
          })
        },

        // 获取驻站代表列表
        getRepresentatives () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationMemberList'
          var interfacecontent = {
            isOrderByAreaRole: 1,
            pageNo: 1,
            pageSize: 99,
            query: { isOrderByAreaRole: 1, isUsing: 1 },
            stationId: id,
            tableId: 'id_sys_user_area_member'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationMemberKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.representatives = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getRepresentatives()
            }
          })
        },

        // 获取一站一品
        getFeaturedProduct () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationNewsList'
          var interfacecontent = {
            pageNo: 1,
            pageSize: 1,
            query: { businessCode: 'pin', stationId: id },
            tableId: 'id_station_pin'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationNewsKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              if (ret.data.length > 0) {
                that.featuredProduct = ret.data[0]
                that.featuredProductShow = true
              } else {
                that.featuredProduct = {}
                that.featuredProductShow = false
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getFeaturedProduct()
            }
          })
        },

        // 跳转到留言页面
        goToMessage () {
          window.location.href = 'leaveMessage.html?stationId=' + (id || this.info.id) + '&showRepresentativeInfo=false';
        },

        // 跳转到给代表留言页面
        goToLeaveMessageToRepresentative (member) {
          const params = new URLSearchParams()
          params.append('stationId', id || this.info.id);
          params.append('showRepresentativeInfo', 'true')
          params.append('representativeId', member.id)
          window.location.href = 'leaveMessage.html?' + params.toString()
        },

        // 打开一站一品更多列表
        openFeaturedMore () {
          window.location.href = 'newsList.html?id=' + id + '&type=featuredProduct'
        },

        // tab切换
        onClickTab (name, title) {
          // 重置infoColumnId为空，这样会使用第一个栏目的ID
          infoColumnId = ''
          // 重置所有info_tab_item的选中状态，让第一个处于选中状态
          this.$nextTick(() => {
            document.querySelectorAll('.info_tab_item').forEach((el, index) => {
              el.classList.remove('active')
              if (index === 0) {
                el.classList.add('active')
              }
            })
          })
          if (title == '民情民意') {
            this.getPublicOpinion()
          } else if (title == '信息发布') {
            this.getInformationReleaseColumn()
          } else if (title == '站点活动') {
            this.getActivityDynamicColumn()
          } else if (title == '国家机关进站') {
            this.getStateStationColumn()
          }
        },

        // 搜索
        onSearch (val) {
          this.searchKeyword = val
          if (this.active == '1') {
            this.getPublicOpinion()
          } else if (this.active == '2') {
            this.getInformationRelease()
          } else if (this.active == '3') {
            this.getActivityDynamic()
          } else if (this.active == '4') {
            this.getStateStation()
          }
        },

        // 获取民情民意
        getPublicOpinion () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationLetterList'
          var interfacecontent = {
            keyword: this.searchKeyword,
            pageNo: 1,
            pageSize: 99,
            query: { checkStatus: '1', isUsing: 1, stationId: id },
            tableId: 'id_station_letter'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationLetterKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.publicOpinionData = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getPublicOpinion()
            }
          })
        },

        // 获取信息发布栏目
        getInformationReleaseColumn () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationNewsColumn'
          var interfacecontent = {
            pageNo: 1,
            pageSize: 10,
            query: { businessCode: 'news', isUsing: 1, stationId: id },
            tableId: 'id_station_news'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicReleaseColumnKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.infomationCategoryColumn = ret.data
              this.getInformationRelease()
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getInformationReleaseColumn()
            }
          })
        },

        // 获取信息发布
        getInformationRelease () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationNewsList'
          var interfacecontent = {
            keyword: this.searchKeyword,
            pageNo: 1,
            pageSize: 99,
            query: { businessCode: 'news', columnId: infoColumnId || that.infomationCategoryColumn[0].id || '', isUsing: 1, stationId: id },
            tableId: 'id_station_news'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationNewsKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.infoList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getInformationRelease()
            }
          })
        },

        // 获取站点活动栏目
        getActivityDynamicColumn () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationActivityType'
          var interfacecontent = {
            pageNo: 1,
            pageSize: 10,
            query: { isUsing: 1, stationId: id }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationActivityTypeKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取站点活动栏目====>>', ret)
              that.activityCategoryColumn = ret.data
              this.getActivityDynamic()
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getActivityDynamicColumn()
            }
          })
        },

        // 获取站点活动
        getActivityDynamic () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationActivityList'
          var interfacecontent = {
            keyword: this.searchKeyword,
            pageNo: 1,
            pageSize: 99,
            query: { isUsing: 1, stationId: id },
            stationActivityType: infoColumnId || that.activityCategoryColumn[0].id || '',
            tableId: 'id_station_activity'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationActivityKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取站点活动====>>', ret)
              that.activityList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getActivityDynamic()
            }
          })
        },

        // 获取国家机关进站栏目
        getStateStationColumn () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'dictionarySelector'
          var interfacecontent = { dictCodes: ["station_state_column"] }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStateStationColumnKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取国家机关进站栏目====>>', ret)
              that.stateStationCategoryColumn = ret.data
              this.getStateStation()
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getStateStationColumn()
            }
          })
        },

        // 获取国家机关进站
        getStateStation () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationNewsList'
          var interfacecontent = {
            keyword: this.searchKeyword,
            pageNo: 1,
            pageSize: 99,
            query: { businessCode: 'state', columnId: infoColumnId || that.stateStationCategoryColumn[0].id || '', isUsing: 1, stationId: id },
            tableId: 'id_station_state'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationNewsKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取国家机关进站====>>', ret)
              that.stateStationList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getStateStation()
            }
          })
        },

        // tab选择
        selectTab (item, index) {
          document.querySelectorAll('.info_tab_item').forEach(el => {
            el.classList.remove('active')
          })
          event.target.classList.add('active')
          console.log('选中的tab:', item)
          infoColumnId = item.id
          if (this.active == '1') {
            this.getPublicOpinion()
          } else if (this.active == '2') {
            this.getInformationRelease()
          } else if (this.active == '3') {
            this.getActivityDynamic()
          } else if (this.active == '4') {
            this.getStateStation()
          }
        },

        // 打开民情民意详情
        openStationLetterDetails (item) {
          window.location.href = 'publicOpinionDetails.html?id=' + item.id
        },

        // 打开信息发布详情
        openInformationReleaseDetails (item) {
          window.location.href = 'newsDetails.html?id=' + item.id
        },

        // 打开站点活动详情
        activityItemClick (item) {
          window.location.href = 'siteActivityDetails.html?id=' + item.id
        },

        // 打开国家机关进站详情
        stateStationItemClick (item) {
          window.location.href = 'newsDetails.html?id=' + item.id
        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>

</body>

</html>