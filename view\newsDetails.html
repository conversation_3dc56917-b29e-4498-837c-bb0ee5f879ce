<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      padding: 20px;
    }

    .title {
      width: 100%;
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .time {
      font-size: 15px;
      font-weight: 400;
      color: #999;
      margin-top: 10px;
    }

    .content {
      width: 100%;
    }

    .content {
      width: 100%;
      font-weight: 400;
      font-size: 16px;
      color: #333;
      line-height: 26px;
    }

    .content img {
      width: 350px !important;
      height: auto !important;
      vertical-align: middle;
      margin: 10px auto !important;
      display: block !important;
    }

    .content p {
      font-size: 16px !important;
      background-color: #fff !important;
      margin-top: 2.5vw !important;
    }

    .content img {
      max-width: 360px !important;
      height: auto;
      vertical-align: middle;
    }

    .content .img {
      width: 100%;
      height: 229px;
      margin-top: 10px;
    }

    .content .img img {
      width: 100%;
      height: 100%;
      vertical-align: middle;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app" v-cloak>
    <div class="title" v-cloak>{{details.infoTitle}}</div>
    <div class="time" v-cloak>{{formatTime(details.pubTime)}}</div>
    <div class="content" v-html="details.infoContent"></div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicNewInfoKey = '04f0f6bfe4afe4459b27e4eb7ec7e73f79ac60de860def1b1a8d9892b1cf4608be79b51fb9661c558b246d5296fb9489c05f3a3443cd7c8d73c49d3c8b5ee6c99d' // 获取资讯详情公钥
    var publicStationNewsInfoKey = '046d77857543f467d3e68509a1531d3da629daffbdd57d6e1236b17b3ba80ebd1e128180e642e7d41c2f2f49b29f80456c7e12399daefcf75c7cea6b2267755ee7' // 获取站点资讯详情公钥
    var id = '', type = null
    var app = new Vue({
      el: '#app',
      data: {
        details: {}
      },
      async mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        type = urlParams.get('type')
        console.log('id==>>', id)
        console.log('type==>>', type)
        if (id) {
          if (type == 'featuredProduct') {
            this.getFeaturedInfo()
          } else {
            this.getNewInfo()
          }
        }
      },
      methods: {
        // 获取人大动态详情
        getNewInfo () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'newsInfo'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicNewInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.details = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getNewInfo()
            }
          })
        },

        // 获取一站一品详情
        getFeaturedInfo () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationNewsInfo'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationNewsInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取一站一品详情====>>', ret)
              that.details = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getFeaturedInfo()
            }
          })
        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>