<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>详情</title>
  <link rel="stylesheet" href="../css/vant.css">
  <link rel="stylesheet" href="../css/common.css">
  <style>
    [v-cloak] {
      display: none
    }

    .body_box {
      padding-bottom: 20px;
      background-color: #fff;
      height: 100vh;
    }

    .opinion_details {
      padding: 16px;
    }

    .opinion_details_title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      line-height: 26px;
    }

    .opinion_details_type_status {
      margin-bottom: 12px;
      gap: 8px;
    }

    .opinion_details_status {
      padding: 4px 10px;
      background-color: #e6f7ff;
      color: #1890ff;
      border-radius: 4px;
      font-size: 14px;
    }

    .opinion_details_bannerName {
      padding: 4px 10px;
      background-color: #fff2e8;
      color: #fa8c16;
      border-radius: 4px;
      font-size: 15px;
    }

    .opinion_details_item {
      margin-bottom: 12px;
      font-size: 15px;
      color: #666;
    }

    .opinion_details_item text {
      color: #333;
      margin-left: 4px;
    }

    .opinion_details_content {
      border-radius: 8px;
      font-size: 16px;
      line-height: 24px;
      color: #333;
      text-indent: 2em;
    }

    /* 答复办理情况 */
    .response_status {
      padding: 16px;
      border-top: 10px solid #f8f8f8;
    }

    .response_status_line {
      width: 4px;
      height: 20px;
      background-color: #1890ff;
      margin-right: 12px;
    }

    .response_status_title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .response_status_content {
      padding-bottom: 10px;
    }

    .response_status_content:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 10px;
    }

    .response_status_text {
      font-size: 16px;
      line-height: 24px;
      color: #333;
      margin-bottom: 8px;
    }

    .response_status_file {
      padding: 12px;
      background-color: #f8f8f8;
      border-radius: 8px;
      margin: 12px 0;
    }

    .file_icon {
      width: 32px;
      height: 32px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 600;
      border-radius: 4px;
    }

    .file_icon_pdf {
      background-color: #e02020;
      color: white;
    }

    .file_icon_doc {
      background-color: #1890ff;
      color: white;
    }

    .file_name {
      font-size: 14px;
      color: #666;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .response_status_info {
      font-size: 14px;
      color: #999;
    }

    /* 评价结果 */
    .evaluation_results {
      padding: 16px;
      border-top: 10px solid #f8f8f8;
    }

    .evaluation_timeline {
      margin-top: 16px;
    }

    .timeline_item {
      margin-bottom: 20px;
    }

    .timeline_left {
      width: 20px;
      position: relative;
    }

    .timeline_dot {
      width: 12px;
      height: 12px;
      background-color: #1890ff;
      border-radius: 50%;
      z-index: 1;
    }

    .timeline_line {
      position: absolute;
      top: 12px;
      bottom: -20px;
      width: 2px;
      background-color: #eee;
    }

    .timeline_right {
      flex: 1;
      margin-left: 16px;
    }

    .evaluation_item {
      background-color: #f8f8f8;
      padding: 12px;
      border-radius: 8px;
    }

    .evaluation_status {
      font-weight: 500;
      color: #333;
    }

    .evaluation_time {
      font-size: 12px;
      color: #999;
    }

    .evaluation_content {
      display: block;
      margin-top: 8px;
      font-size: 14px;
      color: #666;
      line-height: 20px;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 详细信息 -->
    <div class="opinion_details" v-cloak>
      <div class="opinion_details_title">{{info.title}}</div>
      <div class="opinion_details_type_status flex_box flex_align_center flex_wrap">
        <div class="opinion_details_status" v-if="info.stationLetterType && info.stationLetterType.value">
          {{info.stationLetterType.name}}</div>
        <div class="opinion_details_bannerName">{{info.letterStatus}}</div>
      </div>
      <div class="opinion_details_item opinion_details_point">
        群众：<text>{{info.senderUserName}}</text>
      </div>
      <div class="opinion_details_item opinion_details_point">
        群众电话：<text>{{info.senderMobile}}</text>
      </div>
      <div class="opinion_details_item opinion_details_end_date">
        反映时间：<text>{{info.receiveTime}}</text>
      </div>
      <div class="opinion_details_item opinion_details_address">
        站点：<text>{{info.stationName}}</text>
      </div>
      <div class="opinion_details_item opinion_details_address">
        站点代表：<text>{{info.receiverName}}</text>
      </div>
      <div class="opinion_details_content" v-html="info.content"></div>
    </div>

    <!-- 答复办理情况 -->
    <div class="response_status" v-if="responseStatusData && responseStatusData.length > 0" v-cloak>
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <span class="section-title">答复办理情况</span>
      </div>
      <div class="response_status_content" v-for="(item, index) in responseStatusData" :key="index">
        <div class="response_status_text" v-html="item.content"></div>
        <div class="response_status_file flex_box flex_align_center" v-for="attachment in item.attachments"
          :key="attachment.id" :data-id="attachment.id" :data-type="attachment.extName" @click="clickfile(attachment)">
          <div :class="['file_icon', attachment.extName=='pdf'?'file_icon_pdf':'file_icon_doc']">
            {{ attachment.extName.toUpperCase() }}
          </div>
          <div class="file_name">{{attachment.originalFileName}}</div>
        </div>
        <div class="response_status_info flex_box flex_justify_between">
          <div class="response_status_author">{{item.answerUser}}</div>
          <div class="response_status_time">{{formatTime(item.answerTime)}}</div>
        </div>
      </div>
    </div>

    <!-- 评价结果 -->
    <div class="evaluation_results" v-if="evaluateList && evaluateList.length > 0" v-cloak>
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <span class="section-title">评价结果</span>
      </div>
      <div class="evaluation_timeline">
        <div class="timeline_item flex_box" v-for="(item, index) in evaluateList" :key="index">
          <div class="timeline_left flex_box flex_direction_column flex_align_center">
            <div class="timeline_dot"></div>
            <div class="timeline_line" v-if="index < evaluateList.length - 1"></div>
          </div>
          <div class="timeline_right">
            <div class="evaluation_item">
              <div class="flex_box" style="justify-content: space-between;">
                <span class="evaluation_status">{{item.evaluateResultName}}</span>
                <span class="evaluation_time">{{formatTime(item.evaluateTime)}}</span>
              </div>
              <span class="evaluation_content">{{item.content}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    // 获取我的留言私钥
    var areaKey = '00871c7d56551fb20bde67a129649e7faf95c05d9203d47ed6de529df5722303b4'
    // 获取我的留言公钥
    var publicKey = '045b4e75032df41d536233631f426ef247ac738049ab291d1612df02801cb1f37cf9ef7949e5f00cecdb55411b3822deef5b03b8ab48fa8b45ebc546d1d88c32f5'

    var app = new Vue({
      el: '#app',
      data: {
        info: {
          title: '关于改善社区环境的建议',
          stationLetterType: {
            value: '1',
            name: '环境整治'
          },
          letterStatus: '已回复',
          senderUserName: '张三',
          senderMobile: '138****1234',
          receiveTime: '2025-10-20 14:30:00',
          stationName: '朝阳区第一社区工作站',
          receiverName: '李四',
          content: '<p>尊敬的社区工作站领导：</p><p>我是本社区居民张三，近期发现社区内存在以下问题需要改善：</p><p>1. 社区内部分垃圾桶周边环境较差，有异味和散落垃圾</p><p>2. 健身器材区域缺乏维护，部分器材损坏</p><p>3. 社区路灯部分不亮，影响夜间出行安全</p><p>希望相关部门能够重视并尽快解决这些问题，共建美好社区环境。</p><p>此致</p><p>敬礼</p>'
        },
        responseStatusData: [{ "id": "1984099231429844993", "letterId": "1982824730750640130", "answerUserId": "1", "answerUser": "Admin", "answerUserType": "", "content": "回复内容22", "checkStatus": null, "answerTime": 1761881085062, "attachmentIds": "5f1f8c57-2c9c-4bb3-8afe-88d2f7711439", "attachments": [{ "id": "5f1f8c57-2c9c-4bb3-8afe-88d2f7711439", "originalFileName": "“山东通”安装使用说明202210-2.pdf", "extName": "pdf", "newFileName": "5f1f8c57-2c9c-4bb3-8afe-88d2f7711439.pdf", "fileSize": 798274, "isInitFile": 0, "isKeepAlive": 0, "createBy": "1", "createByName": "Admin", "createDate": 1761881093630, "tracy": null }] }, { "id": "1984099160441249794", "letterId": "1982824730750640130", "answerUserId": "1", "answerUser": "Admin", "answerUserType": "", "content": "回复内容", "checkStatus": null, "answerTime": 1761881066370, "attachmentIds": "87eb5aca-f889-4632-a49e-2ef0a0ef2a33,c7402d27-2c91-4c91-818d-472d0b05a631", "attachments": [{ "id": "87eb5aca-f889-4632-a49e-2ef0a0ef2a33", "originalFileName": "1点点图表代码.doc", "extName": "doc", "newFileName": "87eb5aca-f889-4632-a49e-2ef0a0ef2a33.doc", "fileSize": 22016, "isInitFile": 0, "isKeepAlive": 0, "createBy": "1", "createByName": "Admin", "createDate": 1761881072159, "tracy": null }, { "id": "c7402d27-2c91-4c91-818d-472d0b05a631", "originalFileName": "关于出席政协第九届东营市委员会第四次会议的预备通知(2)(1).pdf", "extName": "pdf", "newFileName": "c7402d27-2c91-4c91-818d-472d0b05a631.pdf", "fileSize": 114195, "isInitFile": 0, "isKeepAlive": 0, "createBy": "1", "createByName": "Admin", "createDate": 1761881076651, "tracy": null }] }],
        evaluateList: [{ "id": "1984099362694782977", "letterId": "1982824730750640130", "evaluateUserId": "1", "evaluateUser": "Admin", "evaluateResult": "1", "content": "非常满意", "evaluateTime": 1761881121871, "attachmentIds": null, "checkStatus": 1, "checkTime": 1761881132932, "evaluateResultName": "非常满意" }, { "id": "1984099338455900161", "letterId": "1982824730750640130", "evaluateUserId": "1", "evaluateUser": "Admin", "evaluateResult": "1", "content": "不满意", "evaluateTime": 1761881115433, "attachmentIds": null, "checkStatus": 1, "checkTime": 1761881134365, "evaluateResultName": "非常满意" }, { "id": "1984099314959409154", "letterId": "1982824730750640130", "evaluateUserId": "1", "evaluateUser": "Admin", "evaluateResult": "1", "content": "满意", "evaluateTime": 1761881106640, "attachmentIds": null, "checkStatus": 1, "checkTime": 1761881135749, "evaluateResultName": "非常满意" }]
      },
      mounted () {
      },
      methods: {
        clickfile (attachment) {
          vant.Toast(`点击了文件：${attachment.originalFileName}`);
          // 实际应用中可以添加文件下载或预览逻辑
          console.log('文件信息:', attachment);
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>