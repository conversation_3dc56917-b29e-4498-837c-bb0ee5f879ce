<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>详情</title>
  <link rel="stylesheet" href="../css/vant.css">
  <link rel="stylesheet" href="../css/common.css">
  <style>
    [v-cloak] {
      display: none
    }

    .body_box {
      padding-bottom: 20px;
      background-color: #fff;
      height: 100vh;
    }

    .opinion_details {
      padding: 16px;
    }

    .opinion_details_title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      line-height: 26px;
    }

    .opinion_details_type_status {
      margin-bottom: 12px;
      gap: 8px;
    }

    .opinion_details_status {
      padding: 4px 10px;
      background-color: #e6f7ff;
      color: #1890ff;
      border-radius: 4px;
      font-size: 14px;
    }

    .opinion_details_bannerName {
      padding: 4px 10px;
      background-color: #fff2e8;
      color: #fa8c16;
      border-radius: 4px;
      font-size: 15px;
    }

    .opinion_details_item {
      margin-bottom: 12px;
      font-size: 15px;
      color: #666;
    }

    .opinion_details_item text {
      color: #333;
      margin-left: 4px;
    }

    .opinion_details_content {
      border-radius: 8px;
      font-size: 16px;
      line-height: 24px;
      color: #333;
      text-indent: 2em;
    }

    /* 答复办理情况 */
    .response_status {
      padding: 16px;
      border-top: 10px solid #f8f8f8;
    }

    .response_status_line {
      width: 4px;
      height: 20px;
      background-color: #1890ff;
      margin-right: 12px;
    }

    .response_status_title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .response_status_content {
      padding-bottom: 10px;
    }

    .response_status_content:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 10px;
    }

    .response_status_text {
      font-size: 16px;
      line-height: 24px;
      color: #333;
      margin-bottom: 8px;
    }

    .response_status_file {
      padding: 12px;
      background-color: #f8f8f8;
      border-radius: 8px;
      margin: 12px 0;
    }

    .file_icon {
      width: 32px;
      height: 32px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 600;
      border-radius: 4px;
    }

    .file_icon_pdf {
      background-color: #e02020;
      color: white;
    }

    .file_icon_doc {
      background-color: #1890ff;
      color: white;
    }

    .file_name {
      font-size: 14px;
      color: #666;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .response_status_info {
      font-size: 14px;
      color: #999;
    }

    /* 评价结果 */
    .evaluation_results {
      padding: 16px;
      border-top: 10px solid #f8f8f8;
    }

    .evaluation_timeline {
      margin-top: 16px;
    }

    .timeline_item {
      margin-bottom: 20px;
    }

    .timeline_left {
      width: 20px;
      position: relative;
    }

    .timeline_dot {
      width: 12px;
      height: 12px;
      background-color: #1890ff;
      border-radius: 50%;
      z-index: 1;
    }

    .timeline_line {
      position: absolute;
      top: 12px;
      bottom: -20px;
      width: 2px;
      background-color: #eee;
    }

    .timeline_right {
      flex: 1;
      margin-left: 16px;
    }

    .evaluation_item {
      background-color: #f8f8f8;
      padding: 12px;
      border-radius: 8px;
    }

    .evaluation_status {
      font-weight: 500;
      color: #333;
    }

    .evaluation_time {
      font-size: 12px;
      color: #999;
    }

    .evaluation_content {
      display: block;
      margin-top: 8px;
      font-size: 14px;
      color: #666;
      line-height: 20px;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 详细信息 -->
    <div class="opinion_details" v-cloak>
      <div class="opinion_details_title">{{info.title}}</div>
      <div class="opinion_details_type_status flex_box flex_align_center flex_wrap">
        <div class="opinion_details_status" v-if="info.stationLetterType && info.stationLetterType.value">
          {{info.stationLetterType.name}}</div>
        <div class="opinion_details_bannerName">{{info.letterStatus}}</div>
      </div>
      <div class="opinion_details_item opinion_details_point">
        群众：<span>{{info.senderUserName}}</span>
      </div>
      <div class="opinion_details_item opinion_details_point">
        群众电话：<span>{{info.senderMobile}}</span>
      </div>
      <div class="opinion_details_item opinion_details_end_date">
        反映时间：<span>{{formatTime(info.receiveTime)}}</span>
      </div>
      <div class="opinion_details_item opinion_details_address">
        站点：<span>{{info.stationName}}</span>
      </div>
      <div class="opinion_details_item opinion_details_address">
        站点代表：<span>{{info.receiverName}}</span>
      </div>
      <div class="opinion_details_content" v-html="info.content"></div>
    </div>

    <!-- 答复办理情况 -->
    <div class="response_status" v-if="responseStatusData && responseStatusData.length > 0" v-cloak>
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <span class="section-title">答复办理情况</span>
      </div>
      <div class="response_status_content" v-for="(item, index) in responseStatusData" :key="index">
        <div class="response_status_text" v-html="item.content"></div>
        <div class="response_status_file flex_box flex_align_center" v-for="attachment in item.attachments"
          :key="attachment.id" :data-id="attachment.id" :data-type="attachment.extName" @click="clickfile(attachment)">
          <div :class="['file_icon', attachment.extName=='pdf'?'file_icon_pdf':'file_icon_doc']">
            {{ attachment.extName.toUpperCase() }}
          </div>
          <div class="file_name">{{attachment.originalFileName}}</div>
        </div>
        <div class="response_status_info flex_box flex_justify_between">
          <div class="response_status_author">{{item.answerUser}}</div>
          <div class="response_status_time">{{formatTime(item.answerTime)}}</div>
        </div>
      </div>
    </div>

    <!-- 评价结果 -->
    <div class="evaluation_results" v-if="evaluateList && evaluateList.length > 0" v-cloak>
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <span class="section-title">评价结果</span>
      </div>
      <div class="evaluation_timeline">
        <div class="timeline_item flex_box" v-for="(item, index) in evaluateList" :key="index">
          <div class="timeline_left flex_box flex_direction_column flex_align_center">
            <div class="timeline_dot"></div>
            <div class="timeline_line" v-if="index < evaluateList.length - 1"></div>
          </div>
          <div class="timeline_right">
            <div class="evaluation_item">
              <div class="flex_box" style="justify-content: space-between;">
                <span class="evaluation_status">{{item.evaluateResultName}}</span>
                <span class="evaluation_time">{{formatTime(item.evaluateTime)}}</span>
              </div>
              <span class="evaluation_content">{{item.content}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicOpinionInfoKey = '042addf83a5bc96ef806ae9d9efd3ecd6345170f55d0636e1e5821c2073436db7d800e190bb98e3292783567b784d93cf4e1deaec338d60f09b62cd7983a4ce68f' // 获取民情民意详情
    var publicAnswerKey = '046fa5180f5cd0200e91bc90e71ab0ceffef50d246a04cd805eb3bf5eaa5e560544d826f05a5079e1f4788de71f86ed28f965d89049b8880f55ed8298b5fa72ab1' // 获取答复办理情况
    var publicEvaluateKey = '041a8620861e152831ab77555b3eae54bb428e883ab9924695c319b51c204ff5fabc496b782f845c97d06373718c1a2b98e88575d6283b7a2cbaefc5b4e7eca034' // 获取评价结果
    var id = ''
    var app = new Vue({
      el: '#app',
      data: {
        fileUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/file/preview/',
        info: {},
        responseStatusData: [],
        evaluateList: []
      },
      mounted () {
        // var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id')
        if (id) {
          this.getPublicOpinionDetails()
        }
      },
      methods: {
        // 获取民情民意详情
        getPublicOpinionDetails () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationLetterInfo'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicOpinionInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            var ret = JSON.parse(SM.decrypt(res, privateKey))
            that.info = ret.data
            if (ret.data.hasAnswer) {
              this.getStationLetterAnswerList()
            }
            if (ret.data.hasEvaluate && ret.data.hasEvaluate != '0') {
              this.getStationLetterEvaluateList()
            }
          })
        },

        // 获取答复办理情况
        getStationLetterAnswerList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationLetterAnswerList'
          var interfacecontent = {
            pageNo: 1,
            pageSize: 99,
            query: { letterId: id }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicAnswerKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.responseStatusData = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              // that.getStationLetterAnswerList()
            }
          })
        },

        // 获取评价结果
        getStationLetterEvaluateList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationLetterEvaluateList'
          var interfacecontent = {
            pageNo: 1,
            pageSize: 99,
            query: { letterId: id, checkStatus: 1 }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicEvaluateKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.evaluateList = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              // that.getStationLetterEvaluateList()
            }
          })
        },

        clickfile (attachment) {
          console.log('文件信息:', this.fileUrl + attachment.id);
          window.location.href = 'https://cszysoft.com/appShare/previews/index.html?url=' + this.fileUrl + attachment.id
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>