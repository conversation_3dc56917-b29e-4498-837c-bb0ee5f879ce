<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>立法联系点</title>
  <link rel="stylesheet" href="../css/common.css">
  <link rel="stylesheet" href="../css/vant.css">
  <style>
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: #f5f6fa;
      padding: 20px;
      height: 100vh;
    }

    /* 联系点信息区域 */
    .contact_point_information {
      border-radius: 8px;
      background-color: #fff;
    }

    .contact_point_bg {
      width: 100%;
      height: 124px;
    }

    .contact_point_information_bg {
      text-indent: 2em;
      padding: 12px 20px;
      line-height: 24px;
    }

    .contact_point_information_text {
      font-weight: 400;
      font-size: 15px;
      color: #333333;
    }

    /* 列表 */
    .contact_point_container {
      margin-top: 18px;
      padding: 0 16px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .custom-search-container {
      padding: 15px 0;
    }

    .search-wrapper {
      display: flex;
      align-items: center;
      background-color: #f4f6f8;
      border-radius: 16px;
      height: 36px;
      padding: 0 15px;
      position: relative;
    }

    .search-icon {
      width: 16px;
      height: 16px;
    }

    .search-input {
      flex: 1;
      border: none;
      background: transparent;
      outline: none;
      font-size: 14px;
      color: #333;
      padding-left: 10px;
    }

    .search-input::placeholder {
      color: #999;
    }

    .search-text {
      color: #0D75FF;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      margin-left: 10px;
    }

    /* 筛选组件样式 */
    .filter-container {
      padding: 0 0 10px 0;
      display: flex;
      justify-content: flex-start;
    }

    .filter-item {
      position: relative;
      width: 140px;
    }

    .filter-button {
      font-size: 14px;
      color: #333;
      transition: all 0.3s ease;
    }

    .filter-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      margin-top: 4px;
      overflow: hidden;
    }

    .filter-option {
      padding: 12px 16px;
      font-size: 14px;
      color: #333;
      cursor: pointer;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s ease;
    }

    .filter-option:last-child {
      border-bottom: none;
    }

    .filter-option:hover {
      background-color: #f8f9fa;
    }

    .filter-option.selected {
      color: #0D75FF;
      background-color: #f0f7ff;
    }

    .contact_point_item {
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: flex-start;
    }

    .contact_point_img {
      position: relative;
      margin-right: 10px;
    }

    .contact_point_img_url {
      border-radius: 4px;
      width: 132px;
      height: 100px;
    }

    .contact_point_img_top {
      position: absolute;
      z-index: 1;
      left: 0px;
      top: 0px;
      border-radius: 0 0 10px 0;
      padding: 2px 10px;
      background: linear-gradient(to right, rgb(246,
            146, 28), rgb(246, 99, 28));
    }

    .contact_point_img_top_text {
      color: rgb(255, 255, 255);
      font-size: 12px;
    }

    .contact_point_title {
      font-size: 16px;
      color: #333333;
      display: flex;
      margin-bottom: 8px;
    }

    .contact_point_meta {
      font-weight: 400;
      font-size: 14px;
      color: rgb(105, 105, 105);
      margin-bottom: 6px;
    }

    .contact_point_address {
      font-weight: 400;
      font-size: 14px;
      color: rgb(105, 105, 105);
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 联系点信息 -->
    <div class="contact_point_information">
      <img src="../img/contact_point_top_bg.png" alt="" class="contact_point_bg">
      <div class="contact_point_information_bg">
        <span class="contact_point_information_text" v-cloak>{{information}}</span>
      </div>
    </div>
    <!-- 列表 -->
    <div class="contact_point_container">
      <van-tabs v-model="active" shrink color="#0D75FF" swipeable>
        <van-tab v-for="(item,index) in activeData" :key="item.id" :name="item.id" :title="item.value">
          <!-- 自定义搜索模块 -->
          <div class="custom-search-container">
            <div class="search-wrapper">
              <div class="flex_1 flex_box flex_align_center">
                <img src="../img/search.png" alt="搜索图标" class="search-icon" />
                <input type="text" v-model="keyword" placeholder="搜索关键词" class="search-input" />
              </div>
              <div class="search-text" @click="search">搜索</div>
            </div>
          </div>

          <!-- 筛选组件 -->
          <div class="filter-container">
            <div class="filter-item">
              <div class="filter-button" :class="{ active: showSortMenu }" @click="toggleSortMenu">
                <span>↓</span>
                <span>{{ getCurrentSortName() }}</span>
                <span class="arrow">▼</span>
              </div>
              <div v-if="showSortMenu" class="filter-dropdown">
                <div v-for="option in sortOptions" :key="option.key" class="filter-option"
                  :class="{ selected: sortType === option.key }" @click="selectSort(option)">
                  {{ option.name }}
                </div>
              </div>
            </div>
          </div>
          <van-list v-model:loading="loading" :finished="finished" :immediate-check="false" finished-text="没有更多了"
            offset="52" @load="onLoad">
            <div class="contact_point_list">
              <div class="flex_box contact_point_item" v-for="item in contactList" :key="item.id"
                @click="openDetails(item)">
                <div class="contact_point_img" v-cloak>
                  <img v-if="item.coverDrawingId" :src="fileImgUrl+item.coverDrawingId" alt=""
                    class="contact_point_img_url" />
                  <img v-else :src="defaultImg" alt="" class="contact_point_img_url" />
                  <div class="contact_point_img_top">
                    <div class="contact_point_img_top_text">{{item.contactPointLevel?item.contactPointLevel.label:'暂无'}}
                    </div>
                  </div>
                </div>
                <div class="contact_point_item_right" v-cloak>
                  <div class="contact_point_title">{{ item.contactPointName }}</div>
                  <div class="contact_point_meta">{{ item.contactPerson }}-{{item.contactMobile}}</div>
                  <div class="contact_point_address one_text">{{ item.contactPointAddress }}</div>
                </div>
              </div>
            </div>
          </van-list>
        </van-tab>
      </van-tabs>
    </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicLawContactPointListKey = '04b9e5eef7b431111c12987fdc7fc65f71f901b452633d2e93a533880ec1c8ea7b0ffa376ba4794529084a3e28ffd547afca5a40d2fc025a4910ea948e43e72da3' 	// 立法联系点列表公钥
    var app = new Vue({
      el: "#app",
      data: {
        information: '立法联系点是聊城市人大常委会设立的基层立法工作站，旨在收集社会各界对立法工作的意见和建议，提高立法的科学性和**性，让法规更加符合人民群众的切实需求。',
        active: '0',
        activeData: [
          { id: '0', value: '所有联系点' }
        ],
        // 搜索关键词
        keyword: '',
        // 筛选相关
        sortType: '2', // 默认按距离排序
        showSortMenu: false, // 是否显示排序菜单
        sortOptions: [
          { key: '2', name: '按距离最近' },
          { key: '1', name: '按行政区域' }
        ],

        loading: true,
        finished: false,
        fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/',
        contactList: [{ "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "id": "1981253626277953538", "createDate": 1761202649075, "createBy": "1", "updateDate": 1761206008037, "updateBy": "1", "delFlag": 0, "areaId": "371500", "contactPointName": "测试立法联系点", "contactPointLevel": null, "contactPointRd": null, "contactPointProvince": null, "contactPointCity": null, "contactPointCounty": null, "contactPointTown": null, "contactPointAddress": "", "contactPointLongitude": "", "contactPointLatitude": "", "contactPerson": "付", "contactMobile": "18839112150", "contactPointCreateDate": 1761148800000, "coverDrawingId": "", "contactPointIntroduction": "简介简介简介简介简介", "sort": null, "isUsing": 1, "areaParentIds": "371500", "contactPointArea": "-10086", "pointAdminUser": null, "pointAdminUserName": "Admin", "contactPointProvinceName": "", "contactPointCityName": "", "contactPointCountyName": "", "contactPointTownName": "", "distanceLong": 9999, "showDistanceLong": false, "coverDrawingInfos": null, "editButton": true, "contactPointAreaName": "国家" }, { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "id": "1983102973898678273", "createDate": 1761643567907, "createBy": "1", "updateDate": 1761643567907, "updateBy": "1", "delFlag": 0, "areaId": "371500", "contactPointName": "东昌府区立法联系点", "contactPointLevel": null, "contactPointRd": null, "contactPointProvince": null, "contactPointCity": null, "contactPointCounty": null, "contactPointTown": null, "contactPointAddress": "", "contactPointLongitude": "", "contactPointLatitude": "", "contactPerson": "王武", "contactMobile": "15566666666", "contactPointCreateDate": null, "coverDrawingId": "", "contactPointIntroduction": "", "sort": null, "isUsing": 1, "areaParentIds": "371500", "contactPointArea": "371500", "pointAdminUser": null, "pointAdminUserName": "Admin", "contactPointProvinceName": "", "contactPointCityName": "", "contactPointCountyName": "", "contactPointTownName": "", "distanceLong": 9999, "showDistanceLong": false, "coverDrawingInfos": null, "editButton": true, "contactPointAreaName": "聊城市" }, { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "id": "1769568285222793217", "createDate": 1710732930844, "createBy": "1", "updateDate": 1761638657275, "updateBy": "1", "delFlag": 0, "areaId": "371500", "contactPointName": "xx省人大常委会基层立法联系点", "contactPointLevel": null, "contactPointRd": null, "contactPointProvince": null, "contactPointCity": null, "contactPointCounty": null, "contactPointTown": null, "contactPointAddress": "湖南省人民代表大会常务委员会", "contactPointLongitude": "112.992103", "contactPointLatitude": "28.16643", "contactPerson": "罗源", "contactMobile": "18734782841", "contactPointCreateDate": 1552492800000, "coverDrawingId": "b15597fa-1de2-4015-828a-a899c96fb3c8.jpg", "contactPointIntroduction": "暂 无", "sort": 1, "isUsing": 1, "areaParentIds": "371500", "contactPointArea": "-10086", "pointAdminUser": null, "pointAdminUserName": "Admin", "contactPointProvinceName": "", "contactPointCityName": "", "contactPointCountyName": "", "contactPointTownName": "", "distanceLong": 9999, "showDistanceLong": false, "coverDrawingInfos": [], "editButton": true, "contactPointAreaName": "国家" }, { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "id": "1769570184848240642", "createDate": 1710733383749, "createBy": "1", "updateDate": 1761638663447, "updateBy": "1", "delFlag": 0, "areaId": "371500", "contactPointName": "（国）中南大学立法联系点", "contactPointLevel": null, "contactPointRd": null, "contactPointProvince": null, "contactPointCity": null, "contactPointCounty": null, "contactPointTown": null, "contactPointAddress": "中南大学铁道校区高速铁路建造技术国家工程实验室", "contactPointLongitude": "112.990623", "contactPointLatitude": "28.136731", "contactPerson": "卞雨", "contactMobile": "17782371931", "contactPointCreateDate": 1610380800000, "coverDrawingId": "e60b586a-be90-4fb9-90f2-99757a01612f.jpg", "contactPointIntroduction": "", "sort": 2, "isUsing": 1, "areaParentIds": "371500", "contactPointArea": "-10086", "pointAdminUser": null, "pointAdminUserName": "Admin", "contactPointProvinceName": "", "contactPointCityName": "", "contactPointCountyName": "", "contactPointTownName": "", "distanceLong": 9999, "showDistanceLong": false, "coverDrawingInfos": [], "editButton": true, "contactPointAreaName": "国家" }],
        defaultImg: '../img/legislation_lxd.png',
        flag: true,
        pageNo: 1,
        pageSize: 15,
      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        this.getLegislativeLiaisonPointsList()
        // 添加点击外部关闭下拉菜单的事件监听
        document.addEventListener('click', this.handleClickOutside)
      },
      beforeDestroy () {
        // 移除事件监听器
        document.removeEventListener('click', this.handleClickOutside)
      },
      methods: {
        // 搜索
        search () {
          this.pageNo = 1
          this.contactList = []
          this.finished = false
          this.getLegislativeLiaisonPointsList()
        },
        // 切换排序下拉菜单
        toggleSortMenu () {
          this.showSortMenu = !this.showSortMenu
        },
        // 选择排序选项
        selectSort (option) {
          this.sortType = option.key
          this.showSortMenu = false
          // 重新加载数据
          this.pageNo = 1
          this.contactList = []
          this.finished = false
          this.getLegislativeLiaisonPointsList()
        },
        // 获取当前排序名称
        getCurrentSortName () {
          const currentSort = this.sortOptions.find(item => item.key === this.sortType)
          return currentSort ? currentSort.name : '按距离最近'
        },
        // 处理点击外部关闭下拉菜单
        handleClickOutside (event) {
          const filterContainer = event.target.closest('.filter-container')
          if (!filterContainer) {
            this.showSortMenu = false
          }
        },
        // 下拉加载
        onLoad () {
          if (this.flag) {
            this.getLegislativeLiaisonPointsList()
          }
        },
        // 获取立法联系点列表
        getLegislativeLiaisonPointsList () {
          var that = this
          that.flag = false
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'lawContactPointList'
          var interfacecontent = {
            appSort: this.sortType,
            keyword: this.keyword,
            orderBys: [{ columnId: "law_contact_pointsort", isDesc: "0" }],
            pageNo: this.pageNo,
            pageSize: this.pageSize,
            pointAdminWeb: true,
            tableId: 'law_contact_point'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicLawContactPointListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取立法联系点列表', ret)
              var { total } = ret
              if (that.pageNo === 1) {
                that.contactList = ret.data || []
              } else {
                that.contactList = that.contactList.concat(ret.data)
              }
              that.pageNo = that.pageNo + 1
              that.loading = false
              that.flag = true
              // 数据全部加载完成
              if (that.contactList.length >= total) {
                that.finished = true
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getLegislativeLiaisonPointsList()
            }
          })
        },
        // 进详情
        openDetails (_item) {
          window.location.href = './legislativeLiaisonPointsDetails.html?id=' + _item.id
        }
      }
    })
  </script>
</body>

</html>