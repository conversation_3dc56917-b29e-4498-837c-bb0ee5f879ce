<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>立法联系点</title>
  <link rel="stylesheet" href="../css/common.css">
  <link rel="stylesheet" href="../css/vant.css">
  <style>
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: #f5f6fa;
      padding: 20px;
      height: 100vh;
    }

    /* 联系点信息区域 */
    .contact_point_information {
      border-radius: 8px;
      background-color: #fff;
    }

    .contact_point_bg {
      width: 100%;
      height: 124px;
    }

    .contact_point_information_bg {
      text-indent: 2em;
      padding: 12px 20px;
      line-height: 24px;
    }

    .contact_point_information_text {
      font-weight: 400;
      font-size: 15px;
      color: #333333;
    }

    /* 列表 */
    .contact_point_container {
      margin-top: 18px;
      padding: 0 16px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .custom-search-container {
      padding: 15px 0;
    }

    .search-wrapper {
      display: flex;
      align-items: center;
      background-color: #f4f6f8;
      border-radius: 16px;
      height: 36px;
      padding: 0 10px;
      position: relative;
    }

    .search-input {
      flex: 1;
      border: none;
      background: transparent;
      outline: none;
      font-size: 14px;
      color: #333;
      padding-left: 24px;
    }

    .search-input::placeholder {
      color: #999;
    }

    .search-icon {
      position: absolute;
      left: 85px;
      width: 16px;
      height: 16px;
    }

    .search-text {
      color: #0D75FF;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      margin-left: 10px;
    }

    .contact_point_img {
      position: relative;
      margin-right: 10px;
    }

    .contact_point_img_url {
      border-radius: 4px;
      width: 132px;
      height: 104px;
      object-fit: cover;
    }

    .contact_point_img_top {
      position: absolute;
      z-index: 1;
      left: 0px;
      top: 0px;
      border-radius: 10px 0;
      padding: 2px 10px;
      background: linear-gradient(270deg, #3894FF 0%, #41BBFF 100%);
    }

    .contact_point_img_top_text {
      color: rgb(255, 255, 255);
      font-size: 12px;
    }

    .workstation_item_title {
      font-size: 17px;
      color: #333333;
      display: flex;
      margin-bottom: 3px;
    }

    .icon_box {
      width: 16px;
      height: 16px;
      margin-top: 2px;
      margin-right: 2px;
    }

    .text_box {
      font-size: 12px;
      color: #696969;
      flex: 1;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 联系点信息 -->
    <div class="contact_point_information">
      <img src="../img/contact_point_top_bg.png" alt="" class="contact_point_bg">
      <div class="contact_point_information_bg">
        <span class="contact_point_information_text" v-cloak>{{information}}</span>
      </div>
    </div>
    <!-- 列表 -->
    <div class="contact_point_container">
      <van-tabs v-model="active" shrink color="#0D75FF" swipeable>
        <van-tab v-for="(item,index) in activeData" :key="item.id" :name="item.id" :title="item.value">
          <!-- 自定义搜索模块 -->
          <div class="custom-search-container">
            <div class="search-wrapper">
              <!-- 中间搜索输入框 -->
              <input type="text" v-model="keyword" placeholder="搜索关键词" class="search-input" />
              <img src="../img/search.png" alt="搜索图标" class="search-icon" />
              <!-- 右侧搜索文本 -->
              <div class="search-text" @click="search">
                搜索
              </div>
            </div>
          </div>
          <van-pull-refresh v-model="refreshing" style="min-height: 300px" @refresh="onRefresh">
            <van-list v-model:loading="loading" :finished="finished" :immediate-check="false" finished-text="没有更多了"
              offset="52" @load="onLoad">
              <div class="contact_point_list">
                <div class="loading" v-if="loadingShow"></div>
                <template v-else-if="contactList&&contactList.length!==0">
                  <div class="flex_box" v-for="item in contactList" :key="item.id" @click="openDetails(item)">
                    <div class="contact_point_img" v-cloak>
                      <img v-if="item.imgUrl" :src="item.imgUrl" alt="" class="contact_point_img_url" v-cloak />
                      <img v-else :src="defaultImg" alt="" class="contact_point_img_url" v-cloak />
                    </div>
                    <div class="contact_point_item_right">
                      <div class="contact_point_item_title" v-cloak>{{ item.name }}</div>
                      <div class="flex_box flex_align_center flex_justify_between" style="margin-bottom: 3px;">
                        <div class="flex_box flex_align_center">
                          <img v-cloak src="../img/icon_user.png" alt="" class="icon_box" />
                          <span v-cloak class="text_box">{{item.contactUserName}}</span>
                        </div>
                        <div class="flex_box flex_align_center" v-if="item.contactTelephone" v-cloak>
                          <img src="../img/icon_phone.png" alt="" class="icon_box" />
                          <span class="text_box">{{ item.contactTelephone }}</span>
                        </div>
                      </div>
                      <div class="flex_box flex_align_center" style="margin-bottom: 3px;" v-if="item.openTime" v-cloak>
                        <img src="../img/icon_time.png" alt="" class="icon_box" />
                        <span class="text_box">{{ item.openTime }}</span>
                      </div>
                      <div class="flex_box flex_align_center" v-if="item.address" v-cloak>
                        <img src="../img/icon_location.png" alt="" class="icon_box" />
                        <span class="text_box one_text">{{ item.address }}</span>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </van-list>
          </van-pull-refresh>
        </van-tab>
      </van-tabs>
    </div>
  </div>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var allListKey = '4e2524722f0b7de6bc9a49b4e955a16809c0a8630d8b3b38388bc15e2c67bdc8' // 私钥
    var publicAllListKey = '04e68f60ab0efc9c89cb966d6dd43f1bd6de9a47ee19395c84a00c87cfc8af135466b4b97fdb373f23db9c72d90a14f3cfc2d6aaf8f67e0dffab36dad384cf2319' 	// 立法联系点公钥
    var app = new Vue({
      el: "#app",
      data: {
        information: '立法联系点是聊城市人大常委会设立的基层立法工作站，旨在收集社会各界对立法工作的意见和建议，提高立法的科学性和**性，让法规更加符合人民群众的切实需求。',
        active: '0',
        activeData: [
          { id: '0', value: '所有联系点' }
        ],
        // 搜索关键词
        keyword: '',
        // 筛选相关
        sortType: '2', // 默认按距离排序
        showSortMenu: false, // 是否显示排序菜单
        sortOptions: [
          { key: '2', name: '按距离最近' },
          { key: '1', name: '按行政区域' }
        ],
        loadingShow: false,
        refreshing: false,
        loading: false,
        finished: false,
        contactList: [{ "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "id": "1981253626277953538", "createDate": 1761202649075, "createBy": "1", "updateDate": 1761206008037, "updateBy": "1", "delFlag": 0, "areaId": "371500", "contactPointName": "测试立法联系点", "contactPointLevel": null, "contactPointRd": null, "contactPointProvince": null, "contactPointCity": null, "contactPointCounty": null, "contactPointTown": null, "contactPointAddress": "", "contactPointLongitude": "", "contactPointLatitude": "", "contactPerson": "付", "contactMobile": "18839112150", "contactPointCreateDate": 1761148800000, "coverDrawingId": "", "contactPointIntroduction": "简介简介简介简介简介", "sort": null, "isUsing": 1, "areaParentIds": "371500", "contactPointArea": "-10086", "pointAdminUser": null, "pointAdminUserName": "Admin", "contactPointProvinceName": "", "contactPointCityName": "", "contactPointCountyName": "", "contactPointTownName": "", "distanceLong": 9999, "showDistanceLong": false, "coverDrawingInfos": null, "editButton": true, "contactPointAreaName": "国家" }, { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "id": "1983102973898678273", "createDate": 1761643567907, "createBy": "1", "updateDate": 1761643567907, "updateBy": "1", "delFlag": 0, "areaId": "371500", "contactPointName": "东昌府区立法联系点", "contactPointLevel": null, "contactPointRd": null, "contactPointProvince": null, "contactPointCity": null, "contactPointCounty": null, "contactPointTown": null, "contactPointAddress": "", "contactPointLongitude": "", "contactPointLatitude": "", "contactPerson": "王武", "contactMobile": "15566666666", "contactPointCreateDate": null, "coverDrawingId": "", "contactPointIntroduction": "", "sort": null, "isUsing": 1, "areaParentIds": "371500", "contactPointArea": "371500", "pointAdminUser": null, "pointAdminUserName": "Admin", "contactPointProvinceName": "", "contactPointCityName": "", "contactPointCountyName": "", "contactPointTownName": "", "distanceLong": 9999, "showDistanceLong": false, "coverDrawingInfos": null, "editButton": true, "contactPointAreaName": "聊城市" }, { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "id": "1769568285222793217", "createDate": 1710732930844, "createBy": "1", "updateDate": 1761638657275, "updateBy": "1", "delFlag": 0, "areaId": "371500", "contactPointName": "xx省人大常委会基层立法联系点", "contactPointLevel": null, "contactPointRd": null, "contactPointProvince": null, "contactPointCity": null, "contactPointCounty": null, "contactPointTown": null, "contactPointAddress": "湖南省人民代表大会常务委员会", "contactPointLongitude": "112.992103", "contactPointLatitude": "28.16643", "contactPerson": "罗源", "contactMobile": "18734782841", "contactPointCreateDate": 1552492800000, "coverDrawingId": "b15597fa-1de2-4015-828a-a899c96fb3c8.jpg", "contactPointIntroduction": "暂 无", "sort": 1, "isUsing": 1, "areaParentIds": "371500", "contactPointArea": "-10086", "pointAdminUser": null, "pointAdminUserName": "Admin", "contactPointProvinceName": "", "contactPointCityName": "", "contactPointCountyName": "", "contactPointTownName": "", "distanceLong": 9999, "showDistanceLong": false, "coverDrawingInfos": [], "editButton": true, "contactPointAreaName": "国家" }, { "jordan": null, "james": null, "kobe": null, "duncan": null, "wade": null, "durant": null, "johnson": null, "curry": null, "tracy": null, "jordanDict": null, "jamesDict": null, "kobeDicts": [], "duncanDicts": [], "jordanDictName": null, "jamesDictName": null, "kobeDictsName": null, "duncanDictsName": null, "id": "1769570184848240642", "createDate": 1710733383749, "createBy": "1", "updateDate": 1761638663447, "updateBy": "1", "delFlag": 0, "areaId": "371500", "contactPointName": "（国）中南大学立法联系点", "contactPointLevel": null, "contactPointRd": null, "contactPointProvince": null, "contactPointCity": null, "contactPointCounty": null, "contactPointTown": null, "contactPointAddress": "中南大学铁道校区高速铁路建造技术国家工程实验室", "contactPointLongitude": "112.990623", "contactPointLatitude": "28.136731", "contactPerson": "卞雨", "contactMobile": "17782371931", "contactPointCreateDate": 1610380800000, "coverDrawingId": "e60b586a-be90-4fb9-90f2-99757a01612f.jpg", "contactPointIntroduction": "", "sort": 2, "isUsing": 1, "areaParentIds": "371500", "contactPointArea": "-10086", "pointAdminUser": null, "pointAdminUserName": "Admin", "contactPointProvinceName": "", "contactPointCityName": "", "contactPointCountyName": "", "contactPointTownName": "", "distanceLong": 9999, "showDistanceLong": false, "coverDrawingInfos": [], "editButton": true, "contactPointAreaName": "国家" }],
        defaultImg: '../img/workstation_default.png',
        flag: true,
        pageNo: 1,
        pageSize: 15,
      },
      mounted () {
        // this.getAllList()
      },
      methods: {
        // 搜索
        search () {
          this.pageNo = 1
          this.contactList = []
          this.finished = false
          // this.getAllList()
        },
        // 刷新
        onRefresh () {
          this.pageNo = 1
          this.contactList = []
          this.finished = false
          // this.getAllList()
        },
        // 下拉加载
        onLoad () {
          if (this.flag) {
            // this.getAllList()
          }
        },
        // 获取立法联系点列表
        getAllList () {
          var that = this
          that.flag = false
          var appid = 'dbllztuhek'
          var interfaceid = 'qdrdllzAllSiteList'
          var interfacecontent = { "pageNo": that.pageNo, "pageSize": that.pageSize, "keyword": that.keyword, "district": this.regionId }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicAllListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, allListKey))
              console.log('获取立法联系点列表', ret)
              var { total } = ret
              if (that.pageNo === 1) {
                that.contactList = ret.data || []
              } else {
                that.contactList = that.contactList.concat(ret.data)
              }
              that.loadingShow = false
              that.pageNo = that.pageNo + 1
              that.loading = false
              that.refreshing = false
              that.flag = true
              // 数据全部加载完成
              if (that.contactList.length >= total) {
                that.finished = true
              }
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getAllList()
            }
          })
        },
        // 进详情
        openDetails (_item) {
          window.location.href = './legislativeLiaisonPointsDetails.html?id=' + _item.id
        }
      }
    })
  </script>
</body>

</html>