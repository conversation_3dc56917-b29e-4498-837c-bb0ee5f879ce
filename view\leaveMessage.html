<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>留言页面</title>
  <link rel="stylesheet" href="../css/common.css">
  <link rel="stylesheet" href="../css/vant.css">
  <style>
    [v-cloak] {
      display: none;
    }

    .body_box {
      background: linear-gradient(rgba(198, 20, 20, 0.1), rgb(255, 255, 255));
      padding: 16px;
    }

    /* 代表信息样式 */
    .representative_info {
      background-color: #fff;
      padding: 16px;
      margin-bottom: 12px;
      border-radius: 8px;
    }

    .representative_avatar {
      width: 80px;
      height: 100px;
      margin-right: 12px;
    }

    .representative_name_tag {
      margin-bottom: 8px;
    }

    .representative_name {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-right: 8px;
    }

    .representative_tag {
      font-size: 12px;
      color: #fff;
      background-color: #1989fa;
      padding: 2px 6px;
      border-radius: 4px;
    }

    .representative_ethnicity,
    .representative_position,
    .representative_station {
      font-size: 13px;
      color: #666;
      margin-bottom: 4px;
    }

    .qr_code_icon {
      width: 24px;
      height: 24px;
    }

    .representative_stats {
      padding-top: 16px;
      margin-top: 16px;
      border-top: 1px solid #eee;
    }

    .stat_item {
      text-align: center;
    }

    .stat_value {
      display: block;
      font-size: 18px;
      font-weight: bold;
      color: #ff6b6b;
      margin-bottom: 4px;
    }

    .stat_label {
      font-size: 12px;
      color: #666;
    }

    .stat_divider {
      width: 1px;
      background-color: #eee;
      height: 30px;
    }

    /* 工作站信息样式 */
    .workstation_info {
      background-color: #fff;
      padding: 16px;
      margin-bottom: 12px;
      border-radius: 4px;
      box-shadow: 0px 0px 4px 1px rgba(24, 64, 118, 0.08);
    }

    .workstation_name {
      font-size: 17px;
      font-weight: bold;
      color: #333;
      margin-right: 8px;
    }

    .workstation_contact {
      margin-top: 12px;
    }

    .contact_item {
      margin-bottom: 8px;
    }

    .contact_label,
    .time_label,
    .address_label {
      font-size: 13px;
      color: #666;
      width: 60px;
    }

    .contact_value,
    .time_value,
    .address_value {
      flex: 1;
      font-size: 13px;
      color: #333;
    }

    .workstation_time,
    .workstation_address {
      display: flex;
      margin-top: 8px;
    }

    /* 标题样式 */
    .big_title_box {
      padding: 5px 16px;
      margin-bottom: 12px;
    }

    .title_left_line,
    .title_right_line {
      width: 30px;
      height: 2px;
      background: rgb(198, 20, 20);
    }

    .big_title {
      font-size: 17px;
      font-weight: bold;
      color: rgb(198, 20, 20);
      padding: 0 16px;
    }

    /* 表单样式 */
    .message-form {
      background-color: #fff;
      padding: 0 16px;
    }

    .form_item {
      padding: 12px 0;
      border-bottom: 1px solid #f5f5f5;
    }

    .form_label {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }

    .form_label.required::after {
      content: '*';
      color: #ee0a24;
      margin-left: 4px;
    }

    .form_select {
      padding: 8px 0;
    }

    .select_text {
      font-size: 14px;
      color: #999;
    }

    .select_icon {
      width: 16px;
      height: 16px;
    }

    .form_input,
    .form_textarea {
      width: 100%;
      font-size: 14px;
      color: #333;
      border: none;
      outline: none;
      padding: 8px 0;
    }

    .form_textarea {
      min-height: 100px;
      resize: none;
    }

    .placeholder {
      color: #999;
    }

    .upload-area {
      gap: 10px;
      margin-top: 8px;
    }

    .upload-item {
      width: 80px;
      height: 80px;
      border: 1px dashed #dcdfe6;
      border-radius: 4px;
      position: relative;
    }

    .upload-btn {
      width: 100%;
      height: 100%;
    }

    .upload-btn-text {
      font-size: 32px;
      color: #909399;
    }

    .upload-img {
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }

    .delete-btn {
      position: absolute;
      top: -8px;
      right: -8px;
    }

    .delete-icon {
      width: 20px;
      height: 20px;
    }

    .button_group {
      margin-top: 20px;
      padding-bottom: 20px;
      gap: 10px;
    }

    .my_message_button,
    .submit_button {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      font-size: 16px;
    }

    .my_message_button {
      background-color: #fff;
      color: #E63336;
      border: 1px solid #E63336;
    }

    .submit_button {
      background-color: #E63336;
      color: #fff;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <!-- 代表信息（如果有） -->
    <div class="representative_info" v-if="showRepresentativeInfo">
      <div class="flex_box flex_align_center">
        <img :src="representativeInfo.photo ? fileImgUrl + representativeInfo.photo : '../img/def_head_img.jpg'"
          class="representative_avatar" />
        <div class="flex_1 flex_box flex_justify_between">
          <div>
            <div class="representative_name_tag" v-cloak>
              <span class="representative_name">{{representativeInfo.userName}}</span>
              <span class="representative_tag">{{representativeInfo.areaName}}人大代表</span>
            </div>
            <div class="representative_ethnicity" v-cloak v-if="representativeInfo.nation">
              民族：{{representativeInfo.nation?.name}}</div>
            <div class="representative_position" v-cloak>单位职务：{{representativeInfo.position}}</div>
            <div class="representative_station" v-cloak>入驻站点：{{representativeInfo.stationNames}}</div>
          </div>
          <!-- <img class="qr_code_icon" src="../img/icon_qrcode.png" @click="showRepresentativeQRCode" /> -->
        </div>
      </div>

      <!-- 统计数据 -->
      <div class="representative_stats flex_box flex_justify_around">
        <div class="stat_item" v-cloak>
          <span class="stat_value">{{activityCount}}</span>
          <span class="stat_label">活动参与</span>
        </div>
        <div class="stat_divider"></div>
        <div class="stat_item" v-cloak>
          <span class="stat_value">{{opinionCount}}</span>
          <span class="stat_label">受理民意</span>
        </div>
        <div class="stat_divider"></div>
        <div class="stat_item" v-cloak>
          <span class="stat_value">{{dutyCount}}</span>
          <span class="stat_label">代表值班</span>
        </div>
      </div>
    </div>

    <!-- 工作站信息（如果没有代表信息） -->
    <div class="workstation_info" v-if="!showRepresentativeInfo && workstationInfo">
      <div class="flex_box flex_justify_between" v-cloak>
        <span class="workstation_name one_text">{{workstationInfo.name}}</span>
        <!-- <img class="qr_code_icon" src="../img/icon_qrcode.png" @click="showWorkstationQRCode" /> -->
      </div>
      <div class="workstation_contact" v-cloak>
        <div class="contact_item flex_box" v-cloak>
          <span class="contact_label">联系人</span>
          <span class="contact_value">{{workstationInfo.contactUserName}}</span>
        </div>
        <div class="contact_item flex_box" v-cloak>
          <span class="contact_label">电话</span>
          <span class="contact_value">{{workstationInfo.contactTelephone}}</span>
        </div>
      </div>
      <div class="workstation_time" v-cloak>
        <span class="time_label">接待时间</span>
        <span class="time_value" v-cloak>{{workstationInfo.openTime}}</span>
      </div>
      <div class="workstation_address" v-cloak>
        <span class="address_label">站点地址</span>
        <span class="address_value" v-cloak>{{workstationInfo.address}}</span>
      </div>
    </div>

    <!-- 给xx留言 -->
    <div class="flex_box flex_align_center flex_justify_center big_title_box" v-cloak>
      <div class="title_left_line"></div>
      <span class="big_title">{{formTitle}}</span>
      <div class="title_right_line"></div>
    </div>

    <!-- 留言表单 -->
    <div class="message-form">
      <div class="form-section">
        <!-- 分类选择 -->
        <div class="form_item" v-cloak>
          <div class="form_label required">建议/意见分类</div>
          <div class="flex_box flex_justify_between flex_align_center" @click="openCategoryPicker">
            <span class="select_text">{{selectedCategory || '请选择建议/意见分类'}}</span>
            <img src="../img/arrow_right.png" class="select_icon" />
          </div>
        </div>

        <!-- 标题输入 -->
        <div class="form_item">
          <div class="form_label required">建议/意见标题</div>
          <input class="form_input" placeholder="请输入建议/意见标题" placeholder-class="placeholder" v-model="messageTitle" />
        </div>

        <!-- 内容输入 -->
        <div class="form_item">
          <div class="form_label required">建议/意见内容</div>
          <textarea class="form_textarea" placeholder="请输入建议/意见内容" placeholder-class="placeholder"
            v-model="messageContent"></textarea>
        </div>

        <!-- 问题随拍 -->
        <div class="form_item" v-cloak>
          <div class="form_label">问题随拍 ({{imageList.length}}/9)</div>
          <div class="upload-area flex_box flex_wrap">
            <div class="upload-item" @click="chooseImage" v-if="imageList.length < 9">
              <div class="upload-btn flex_box flex_justify_center flex_align_center">
                <div class="upload-btn-text">+</div>
              </div>
            </div>
            <div class="upload-item" v-for="(item, index) in imageList" :key="index" @click="previewImage(index)">
              <img :src="item.url" class="upload-img" />
              <div class="delete-btn flex_box flex_align_center flex_justify_center" @click.stop="deleteImage(index)">
                <img src="../img/close.png" class="delete-icon" />
              </div>
            </div>
          </div>
        </div>

        <!-- 姓名 -->
        <div class="form_item">
          <div class="form_label required">姓名</div>
          <input class="form_input" placeholder="请输入姓名" placeholder-class="placeholder" v-model="userName" />
        </div>

        <!-- 电话 -->
        <div class="form_item">
          <div class="form_label required">电话</div>
          <input class="form_input" type="number" placeholder="请输入电话" placeholder-class="placeholder"
            v-model="userPhone" maxlength="11" />
        </div>

        <!-- 居住地址 -->
        <div class="form_item">
          <div class="form_label">居住地址</div>
          <input class="form_input" placeholder="请输入居住地址" placeholder-class="placeholder" v-model="userAddress" />
        </div>
      </div>

      <!-- 操作按钮组 -->
      <div class="button_group flex_box">
        <div class="my_message_button flex_box flex_align_center flex_justify_center" @click="goToMyMessages">我的留言</div>
        <div class="submit_button flex_box flex_align_center flex_justify_center" @click="submitMessage">提交</div>
      </div>
    </div>

    <!-- 分类选择器 -->
    <van-popup v-model="showCategoryPicker" position="bottom" round>
      <van-picker title="选择建议/意见分类" :columns="categoryList" show-toolbar @confirm="confirmCategory"
        @cancel="cancelCategory" />
    </van-popup>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicStationMemberInfoKey = '04cc4c70c8f51c1660ce119870601e4dd17a7a2ef555b92f59545d86a1c924493d7391a35c0dadefb2dfbba1a70a934d17e66c3acc0b7c1adf53d60940cd603711' 	// 获取代表详情公钥
    var publicStationTotalKey = '0432b75ee12b588301228ef4a0f528925261a0d58c3b2bdd65453e8c3b3dd7ce1d782efbe8c8feb8dfeceb50908499b8954034e7ac7ddd24862f2a5288a5111ce8' // 获取代表详情数量统计公钥
    var publicDictionaryKey = '04395416e84bdf2b98843cc44bdb18b06dd8e53337cfea2ce4ebda7377475adc9757657791ec6cd08134398c2a6bc7ae83d3dceb1df4e76e734accfae7ea9832df'// 获取分类字典公钥
    var publicWorkstationInfoKey = '0484addf5648489d14bd612faa4a01f06e3d5cda1959303dd4ab348a4a3830a2ac4b919a85c603c7e319dc96ed240bbc624e32b2c891d54b52fe066770bd53c211' 	// 获取代表工作站详情公钥
    var publicThemeLetterAddKey = '0416f223878ec0ce934a0de1c730f0aebc80f837b044e50b2d8dbe2b3fe924e52ea7ef82faeba8efda5c5d9a0cd4276ddca0148a4915912a74cd784ccfbcc45340' 	// 提交留言公钥
    var id = '', type = '', stationId = '', representativeId = ''
    var app = new Vue({
      el: '#app',
      data: {
        showRepresentativeInfo: false,
        representativeId: getUrlParam('representativeId'),
        stationId: getUrlParam('stationId'),
        fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/',
        representativeInfo: {},
        activityCount: 0,
        opinionCount: 0,
        dutyCount: 0,
        selectedCategory: '',
        categoryList: [],

        workstationInfo: {},
        formTitle: '',

        showCategoryPicker: false,
        messageTitle: '',
        messageContent: '',
        imageList: [],
        userName: '',
        userPhone: '',
        userAddress: '',
        // 图片上传input元素
        uploadInput: null
      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        type = urlParams.get('type') || 'station'
        stationId = urlParams.get('stationId') || ''
        representativeId = urlParams.get('representativeId') || ''
        // 根据类型设置页面标题和表单标题
        if (type === 'representative') {
          this.formTitle = '给代表留言'
          this.showRepresentativeInfo = true
          if (representativeId) {
            this.getRepresentativeInfo(representativeId)
          }
        } else {
          this.formTitle = '给站点留言'
          this.showRepresentativeInfo = false
          if (stationId) {
            this.getWorkstationInfo(stationId)
          }
        }
        this.dictionarySelector()
        this.initUploadInput()
      },
      methods: {
        // 获取代表信息
        getRepresentativeInfo (id) {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationMemberInfo'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationMemberInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.representativeInfo = ret.data
              this.getRepresentativeCount(ret.data.accountId)
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getRepresentativeInfo()
            }
          })
        },

        // 获取代表信息的数量统计
        getRepresentativeCount (id) {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationTotal'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationTotalKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.activityCount = ret.data.activityCount
              that.opinionCount = ret.data.letterCount
              that.dutyCount = ret.data.dutyCount
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getRepresentativeCount()
            }
          })
        },

        // 获取字典数据
        dictionarySelector () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'dictionarySelector'
          var interfacecontent = { dictCodes: ["station_letter_type"] }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicDictionaryKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.categoryList = ret.data.station_letter_type.map(item => item.name)
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.dictionarySelector()
            }
          })
        },

        // 获取工作室信息
        getWorkstationInfo (id) {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'workerStationInfo'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicWorkstationInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.workstationInfo = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getWorkstationInfo()
            }
          })
        },

        // 初始化上传input
        initUploadInput () {
          this.uploadInput = document.createElement('input');
          this.uploadInput.type = 'file';
          this.uploadInput.accept = 'image/*';
          this.uploadInput.multiple = true;
          this.uploadInput.style.display = 'none';
          this.uploadInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files)
          })
          document.body.appendChild(this.uploadInput)
        },

        // 显示代表二维码
        showRepresentativeQRCode () {
          vant.Toast('显示代表二维码');
        },

        // 显示工作站二维码
        showWorkstationQRCode () {
          vant.Toast('显示工作站二维码');
        },

        // 打开分类选择器
        openCategoryPicker () {
          this.showCategoryPicker = true;
        },

        // 确认分类选择
        confirmCategory (value) {
          this.selectedCategory = value;
          this.showCategoryPicker = false;
        },

        // 取消分类选择
        cancelCategory () {
          this.showCategoryPicker = false;
        },

        // 选择图片
        chooseImage () {
          if (!this.uploadInput) {
            this.initUploadInput();
          }
          // 清空input以允许选择相同文件
          this.uploadInput.value = '';
          // 触发文件选择对话框
          this.uploadInput.click();
        },

        // 处理文件选择
        handleFileSelect (files) {
          if (!files.length) return;
          const remainingSlots = 9 - this.imageList.length;
          const filesToProcess = Math.min(files.length, remainingSlots);
          if (filesToProcess === 0) {
            vant.Toast('最多只能上传9张图片');
            return;
          }
          for (let i = 0; i < filesToProcess; i++) {
            const file = files[i];
            // 检查文件大小（限制为5MB）
            if (file.size > 5 * 1024 * 1024) {
              vant.Toast('图片大小不能超过5MB');
              continue;
            }
            // 使用FileReader预览图片
            const reader = new FileReader();
            reader.onload = (e) => {
              this.imageList.push({
                url: e.target.result,
                file: file
              });
            };
            reader.readAsDataURL(file);
          }
          if (files.length > filesToProcess) {
            vant.Toast(`最多只能上传9张图片，已选择${filesToProcess}张`);
          }
        },

        // 预览图片
        previewImage (index) {
          // 模拟预览图片，实际项目中可以使用vant的ImagePreview组件
          vant.ImagePreview({
            images: this.imageList.map(item => item.url),
            startPosition: index
          });
        },

        // 删除图片
        deleteImage (index) {
          vant.Dialog.confirm({
            title: '确认删除',
            message: '确定要删除这张图片吗？'
          }).then(() => {
            this.imageList.splice(index, 1);
            vant.Toast.success('删除成功');
          }).catch(() => {
            // 取消删除
          });
        },

        // 跳转到我的留言
        goToMyMessages () {
          window.location.href = './myMessages.html'
        },

        // 提交留言
        submitMessage () {
          // 表单验证
          if (!this.selectedCategory) {
            vant.Toast('请选择建议/意见分类');
            return;
          }
          if (!this.messageTitle) {
            vant.Toast('请输入建议/意见标题');
            return;
          }
          if (!this.messageContent) {
            vant.Toast('请输入建议/意见内容');
            return;
          }
          if (!this.userName) {
            vant.Toast('请输入姓名');
            return;
          }
          if (!this.userPhone || !/^1[3-9]\d{9}$/.test(this.userPhone)) {
            vant.Toast('请输入正确的手机号码');
            return;
          }

          // 显示加载中提示
          vant.Toast.loading({
            mask: true,
            message: '提交中...'
          });
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'themeLetterAdd'
          var interfacecontent = {
            form: {
              stationLetterType: this.selectedCategory,
              title: this.messageTitle,
              content: this.messageContent,
              pictures: this.imageList,
              senderUserName: this.userName,
              senderMobile: this.userPhone,
              senderAddr: this.userAddress,
              receiveTime: Date.now(),
              recorder: localStorage.getItem('gname'),
              recorderMobile: localStorage.getItem('gphone'),
              senderId: localStorage.getItem('gphone'),
              stationId,
            }
          }
          console.log('interfacecontent===>', interfacecontent)
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicThemeLetterAddKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('提交留言结果===>>', ret)
              // 关闭加载提示
              //   vant.Toast.clear();
              //   vant.Toast.success('留言提交成功！');
            } catch (error) {
              // 在这里处理异常情况
              console.log('提交留言结果发生错误：', error)
            }
          })
        }
      }
    })

    // 获取URL参数
    function getUrlParam (name) {
      const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
      const r = window.location.search.substr(1).match(reg);
      if (r != null) return decodeURIComponent(r[2]);
      return null;
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', function () {
      if (app && app.uploadInput && app.uploadInput.parentNode) {
        document.body.removeChild(app.uploadInput);
      }
    })
  </script>
</body>

</html>